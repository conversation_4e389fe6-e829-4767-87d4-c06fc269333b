[2025-05-27 13:13:05] local.INFO: Raw date override successful for penawaran - tanggal_penawaran: 2025-05-24  
[2025-05-27 13:13:14] local.INFO: Validating part at index 0 {"part_data":{"part_inventory_id":815,"quantity":1,"price":"5200000.00","status":"Not Ready","is_custom":false,"part_code":"C-SD-8126-PWB","part_name":"COMPRESSOR SD7H15 S8126 DOUBLE PULLEY A"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 0 is regular, validating part_inventory_id  
[2025-05-27 13:13:14] local.INFO: Validating part at index 1 {"part_data":{"part_inventory_id":828,"quantity":1,"price":"2650000.00","status":"Not Ready","is_custom":false,"part_code":"CD-142320-PWB","part_name":"CONDENSOR R134 14 x 23 x 20MM"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 1 is regular, validating part_inventory_id  
[2025-05-27 13:13:14] local.INFO: Validating part at index 2 {"part_data":{"part_inventory_id":1040,"quantity":2,"price":"3850000.00","status":"Not Ready","is_custom":false,"part_code":"MFC-TKD-24-PWB","part_name":"MOTOR FAN CONDENSOR TKD"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 2 is regular, validating part_inventory_id  
[2025-05-27 13:13:14] local.INFO: Validating part at index 3 {"part_data":{"part_inventory_id":1073,"quantity":1,"price":"400000.00","status":"Not Ready","is_custom":false,"part_code":"RD-C19-PWB","part_name":"RECEIVER DRYER R12 NUT 19"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 3 is regular, validating part_inventory_id  
[2025-05-27 13:13:14] local.INFO: Validating part at index 4 {"part_data":{"part_inventory_id":930,"quantity":1,"price":"600000.00","status":"Not Ready","is_custom":false,"part_code":"EV-R12-PC200-PWB","part_name":"EXPANTION VALVE KUNINGAN R12"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 4 is regular, validating part_inventory_id  
[2025-05-27 13:13:14] local.INFO: Validating part at index 5 {"part_data":{"part_inventory_id":100002,"quantity":1,"price":"900000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-5004","part_name":"Hose 5/8 Assy","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 5 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Validating part at index 6 {"part_data":{"part_inventory_id":100003,"quantity":1,"price":"900000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-5446","part_name":"Hose 3/8 Assy 1","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 6 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Validating part at index 7 {"part_data":{"part_inventory_id":100004,"quantity":1,"price":"600000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-4552","part_name":"Hose 3/8 Assy 2","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 7 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Validating part at index 8 {"part_data":{"part_inventory_id":100005,"quantity":1,"price":"850000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-3869","part_name":"Hose 1/2 Assy","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 8 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Validating part at index 9 {"part_data":{"part_inventory_id":100006,"quantity":1,"price":"850000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-1164","part_name":"Vacuum + Oil Compressor + Freon R134a","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 9 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Validating part at index 10 {"part_data":{"part_inventory_id":100007,"quantity":1,"price":"500000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-5055","part_name":"Labour Cost","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 10 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Validating part at index 11 {"part_data":{"part_inventory_id":100006,"quantity":1,"price":"850000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-1164","part_name":"Vacuum + Oil Compressor + Freon R134a","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 11 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Validating part at index 12 {"part_data":{"part_inventory_id":100026,"quantity":1,"price":"2000000.00","status":"Not Ready","is_custom":false,"part_code":".erewr","part_name":".lllll"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 12 is regular, validating part_inventory_id  
[2025-05-27 13:13:14] local.INFO: Validating part at index 13 {"part_data":{"part_inventory_id":100027,"quantity":1,"price":"2000000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-1164s","part_name":"Vacuum + Oil Compressor + Freon R134a","part_type":"AC","purchase_price":700000,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 13 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Validating part at index 14 {"part_data":{"part_inventory_id":100028,"quantity":1,"price":"2000000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250526-9723","part_name":"Vacuuaaaaa","part_type":"PERSEDIAAN LAINNYA","purchase_price":2000000,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 14 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100002,"quantity":1,"price":"900000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-5004","part_name":"Hose 5/8 Assy","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250524-5004"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250524-5004","part_inventory_id":100002} 
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100003,"quantity":1,"price":"900000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-5446","part_name":"Hose 3/8 Assy 1","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250524-5446"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250524-5446","part_inventory_id":100003} 
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100004,"quantity":1,"price":"600000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-4552","part_name":"Hose 3/8 Assy 2","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250524-4552"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250524-4552","part_inventory_id":100004} 
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100005,"quantity":1,"price":"850000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-3869","part_name":"Hose 1/2 Assy","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250524-3869"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250524-3869","part_inventory_id":100005} 
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100006,"quantity":1,"price":"850000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-1164","part_name":"Vacuum + Oil Compressor + Freon R134a","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250524-1164"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250524-1164","part_inventory_id":100006} 
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100007,"quantity":1,"price":"500000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-5055","part_name":"Labour Cost","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250524-5055"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250524-5055","part_inventory_id":100007} 
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100006,"quantity":1,"price":"850000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-1164","part_name":"Vacuum + Oil Compressor + Freon R134a","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250524-1164"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250524-1164","part_inventory_id":100006} 
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100027,"quantity":1,"price":"2000000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-1164s","part_name":"Vacuum + Oil Compressor + Freon R134a","part_type":"AC","purchase_price":700000,"eum":"EA"},"purchase_price_raw":700000,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"AC","purchase_price":700000,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250524-1164s"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250524-1164s","part_inventory_id":100027} 
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100028,"quantity":1,"price":"2000000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250526-9723","part_name":"Vacuuaaaaa","part_type":"PERSEDIAAN LAINNYA","purchase_price":2000000,"eum":"EA"},"purchase_price_raw":2000000,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":2000000,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250526-9723"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250526-9723","part_inventory_id":100028} 
[2025-05-27 13:13:20] local.INFO: getInvoiceData called for penawaran ID: 5  
[2025-05-27 13:13:20] local.INFO: Penawaran does not have invoice  
[2025-05-27 13:13:20] local.INFO: getInvoiceData called for penawaran ID: 5  
[2025-05-27 13:13:20] local.INFO: Penawaran does not have invoice  
[2025-05-27 13:13:24] local.INFO: Raw date override successful for penawaran - tanggal_penawaran: 2025-05-18  
[2025-05-27 13:13:33] local.INFO: Validating part at index 0 {"part_data":{"part_inventory_id":2356,"quantity":1,"price":"21621622.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250519-2928","part_name":"Install New AC","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:33] local.INFO: Part 0 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:33] local.INFO: Validating part at index 1 {"part_data":{"part_inventory_id":799,"quantity":1,"price":"0.00","status":"Not Ready","is_custom":false,"part_code":"C-10S-13C-24-B-ASSY-PWB","part_name":"COMPRESSOR ND 10S 13C 24V SINGLE PULLEY B ASSY"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-05-27 13:13:33] local.INFO: Part 1 is regular, validating part_inventory_id  
[2025-05-27 13:13:33] local.INFO: Validating part at index 2 {"part_data":{"part_inventory_id":100029,"quantity":1,"price":"2000000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250526-3099","part_name":"avavava","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:33] local.INFO: Part 2 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:33] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":2356,"quantity":1,"price":"21621622.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250519-2928","part_name":"Install New AC","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:33] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:33] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250519-2928"} 
[2025-05-27 13:13:33] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250519-2928","part_inventory_id":2356} 
[2025-05-27 13:13:33] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100029,"quantity":1,"price":"2000000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250526-3099","part_name":"avavava","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:33] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:33] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250526-3099"} 
[2025-05-27 13:13:33] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250526-3099","part_inventory_id":100029} 
[2025-05-27 13:13:38] local.INFO: getInvoiceData called for penawaran ID: 4  
[2025-05-27 13:13:38] local.INFO: Penawaran has invoice:  {"invoice_id":20} 
[2025-05-27 13:13:39] local.INFO: getInvoiceData called for penawaran ID: 4  
[2025-05-27 13:13:39] local.INFO: Penawaran has invoice:  {"invoice_id":20} 
