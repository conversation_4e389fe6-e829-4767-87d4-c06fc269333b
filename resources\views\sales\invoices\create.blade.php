@extends('sales.contentsales')
@section('title', 'Tambah Invoice')
@section('resourcesales')
<style>
    .w-fit-content {
        width: fit-content;
    }
    .shadow-kit {
        border: 1px solid rgb(42, 105, 168);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border-radius: 0.5rem;
        background-color: #fff;
    }
    .btn{
        font-size: 11px;
    }
    .btn-secondary{
        background-color: #58c0f6;
        color: #fff;
        border-color: #58c0f6;
    }
    .btn-secondary:hover{
        background-color: #4aa3d9;
        border-color: #4aa3d9;
        color: #fff;
    }

    /* Search box styles */
    .search-box {
        position: relative;
        width: 200px;
    }
    .search-icon {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #aaa;
    }

    /* Date range filter styles */
    .date-range-filter {
        display: flex;
        align-items: center;
    }
    .date-range-filter label {
        margin-right: 5px;
        white-space: nowrap;
    }
</style>
@endsection

@section('contentsales')
@include('sales.partials.navigation')

<div class="content">
    <div class="container-fluid">
        <div class="row">
            <!-- Manual Invoices Table - Left Side -->
            <div class="col-md-7">
                <div class="bgwhite shadow-kit rounded-lg">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h5 class="mb-0 mr-3 font-bold text-uppercase text-white">Daftar Invoice Manual</h5>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="search-box mr-2">
                                <div class="position-relative">
                                    <input type="text" id="search-manual-invoices" class="form-control form-control-sm" placeholder="Cari invoice...">
                                    <i class="mdi mdi-magnify search-icon"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-3 d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="date-range-filter mr-2">
                                    <div class="d-flex">
                                        <div class="mr-2">
                                            <label for="date-from-manual" class="small mb-0">Dari Tanggal:</label>
                                            <input type="date" id="date-from-manual" class="form-control form-control-sm">
                                        </div>
                                        <div>
                                            <label for="date-to-manual" class="small mb-0">Sampai Tanggal:</label>
                                            <input type="date" id="date-to-manual" class="form-control form-control-sm">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered w-100" id="manual-invoices-table" style="font-size: 11px;">
                                <thead class="bg-light">
                                    <tr>
                                        <th>TANGGAL</th>
                                        <th>INVOICE</th>
                                        <th>CUSTOMER</th>
                                        <th>NILAI</th>
                                        <th>STATUS</th>
                                        <th>PARTS</th>
                                        <th>AKSI</th>
                                    </tr>
                                </thead>
                                <tbody id="manual-invoices-table-body">
                                    <!-- Data will be loaded dynamically -->
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <span id="showing-text-manual">Menampilkan 0 dari 0 invoice</span>
                            </div>
                            <div>
                                <nav aria-label="Page navigation">
                                    <ul class="pagination pagination-sm" id="pagination-manual">
                                        <!-- Pagination will be generated dynamically -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Creation/Edit Form - Right Side -->
            <div class="col-md-5">
                <div class="bgwhite shadow-kit rounded-lg">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h5 class="mb-0 mr-3 font-bold text-uppercase text-white" id="form-title">Tambah Invoice</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-light" id="new-invoice-btn" onclick="resetForm()">
                                <i class="mdi mdi-plus"></i> Invoice Baru
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <form id="invoice-form" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="no_invoice" class="form-label">Nomor Invoice</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="no_invoice" name="no_invoice" placeholder="Nomor Invoice" required>
                                            <button class="btn btn-sm btn-outline-secondary" type="button" id="generate-invoice-number">Auto</button>
                                        </div>
                                        <small class="text-muted">Format: 001/INV-PWB/MM/YYYY</small>
                                    </div>
                                    <div class="mb-3">
                                        <label for="customer" class="form-label">Customer</label>
                                        <div class="position-relative">
                                            <input type="text" class="form-control" id="customer" name="customer" placeholder="Ketik kode atau nama customer...">
                                            <input type="hidden" id="customer_code" name="customer_code">
                                            <div id="customer-suggestions" class="dropdown-menu" style="display: none; width: 100%; max-height: 200px; overflow-y: auto;"></div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="po_number" class="form-label">Nomor PO</label>
                                        <input type="text" class="form-control" id="po_number" name="po_number" placeholder="Nomor PO">
                                    </div>
                                    <div class="mb-3">
                                        <label for="sn" class="form-label">Serial Number</label>
                                        <input type="text" class="form-control" id="sn" name="sn" placeholder="Serial Number">
                                    </div>
                                    <div class="mb-3">
                                        <label for="trouble" class="form-label">Kategori</label>
                                        <input type="text" class="form-control" id="trouble" name="trouble" placeholder="Kategori">
                                    </div>
                                    <div class="mb-3">
                                        <label for="lokasi" class="form-label">Lokasi</label>
                                        <input type="text" class="form-control" id="lokasi" name="lokasi" placeholder="Lokasi">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="tanggal_invoice" class="form-label">Tanggal Invoice</label>
                                        <input type="date" class="form-control" id="tanggal_invoice" name="tanggal_invoice" required>
                                        <small class="text-muted" id="tanggal_invoice_display"></small>
                                    </div>
                                    <div class="mb-3">
                                        <label for="due_date" class="form-label">Tanggal Jatuh Tempo</label>
                                        <input type="date" class="form-control" id="due_date" name="due_date">
                                        <small class="text-muted" id="due_date_display"></small>
                                    </div>
                                    <div class="mb-3">
                                        <label for="model_unit" class="form-label">Model Unit</label>
                                        <input type="text" class="form-control" id="model_unit" name="model_unit" placeholder="Model Unit">
                                    </div>
                                    <div class="mb-3">
                                        <label for="hmkm" class="form-label">HM/KM</label>
                                        <input type="text" class="form-control" id="hmkm" name="hmkm" placeholder="HM/KM">
                                    </div>
                                    <div class="mb-3">
                                        <label for="direct_subtotal" class="form-label">Nilai Invoice (Rp)</label>
                                        <input type="text" class="form-control currency-input" id="direct_subtotal" name="direct_subtotal" placeholder="0" required>
                                        <input type="hidden" id="direct_subtotal_raw" name="direct_subtotal_raw">
                                    </div>
                                    <div class="mb-3">
                                        <label for="ppn" class="form-label">PPN (%)</label>
                                        <input type="number" class="form-control" id="ppn" name="ppn" value="11" placeholder="PPN">
                                        <small class="text-muted">Masukkan dalam bentuk persentase (contoh: 11 untuk 11%)</small>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="transfer_to" class="form-label">Ditransfer Ke</label>
                                        <input type="text" class="form-control" id="transfer_to" name="transfer_to" placeholder="Ditransfer Ke">
                                    </div>
                                    <div class="mb-3">
                                        <label for="bank_account" class="form-label">Nomor Rekening</label>
                                        <input type="text" class="form-control" id="bank_account" name="bank_account" placeholder="Nomor Rekening">
                                    </div>
                                    <div class="mb-3">
                                        <label for="bank_branch" class="form-label">Cabang Bank</label>
                                        <input type="text" class="form-control" id="bank_branch" name="bank_branch" placeholder="Cabang Bank">
                                    </div>
                                    <div class="mb-3">
                                        <label for="payment_status" class="form-label">Status Pembayaran</label>
                                        <select class="form-control" id="payment_status" name="payment_status">
                                            <option value="Belum Lunas">Belum Lunas</option>
                                            <option value="Lunas">Lunas</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="notes" class="form-label">Catatan</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Catatan"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="document" class="form-label">Dokumen Invoice</label>
                                        <input type="file" class="form-control" id="document" name="document" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                                        <small class="text-muted">Maksimal 10MB (PDF, JPG, JPEG, PNG, DOC, DOCX)</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Parts Section -->
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <h6 class="mb-3">Daftar Parts (Opsional)</h6>
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="row mb-3">
                                                <div class="col-md-4">
                                                    <label for="part-search" class="form-label">Cari Part</label>
                                                    <input type="text" class="form-control" id="part-search" placeholder="Ketik kode atau nama part...">
                                                    <div id="part-suggestions" class="dropdown-menu" style="display: none; width: 100%; max-height: 200px; overflow-y: auto;"></div>
                                                </div>
                                                <div class="col-md-2">
                                                    <label for="part-quantity" class="form-label">Qty</label>
                                                    <input type="number" class="form-control" id="part-quantity" placeholder="1" min="1">
                                                </div>
                                                <div class="col-md-3">
                                                    <label for="part-price" class="form-label">Harga (Rp)</label>
                                                    <input type="text" class="form-control currency-input" id="part-price" placeholder="0">
                                                    <input type="hidden" id="part-price-raw">
                                                </div>
                                                <div class="col-md-2">
                                                    <label for="part-eum" class="form-label">Satuan</label>
                                                    <input type="text" class="form-control" id="part-eum" placeholder="EA" maxlength="5">
                                                </div>
                                                <div class="col-md-1">
                                                    <label class="form-label">&nbsp;</label>
                                                    <button type="button" class="btn btn-sm btn-primary d-block" id="add-part-btn">
                                                        <i class="mdi mdi-plus"></i>
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Parts Table -->
                                            <div class="table-responsive">
                                                <table class="table table-bordered" id="parts-table">
                                                    <thead class="bg-light">
                                                        <tr>
                                                            <th>Kode Part</th>
                                                            <th>Nama Part</th>
                                                            <th>Qty</th>
                                                            <th>Harga</th>
                                                            <th>Satuan</th>
                                                            <th>Total</th>
                                                            <th>Aksi</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="parts-table-body">
                                                        <!-- Parts will be added here dynamically -->
                                                    </tbody>
                                                    <tfoot>
                                                        <tr class="bg-light">
                                                            <th colspan="5" class="text-right">Total Parts:</th>
                                                            <th id="parts-total">Rp 0</th>
                                                            <th></th>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="d-flex justify-content-end">
                                        <a href="{{ route('sales.invoices') }}" class="btn btn-sm btn-secondary me-2">Batal</a>
                                        <button type="submit" class="btn btn-sm btn-primary" id="save-invoice-btn">Simpan Invoice</button>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="invoice_id" name="invoice_id">
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set default date values
        const today = new Date();
        document.getElementById('tanggal_invoice').valueAsDate = today;
        updateDateDisplay('tanggal_invoice');

        // Calculate due date (60 days from today)
        const dueDate = new Date(today);
        dueDate.setDate(dueDate.getDate() + 60);
        document.getElementById('due_date').valueAsDate = dueDate;
        updateDateDisplay('due_date');

        // Parts management variables
        let selectedParts = [];
        let searchTimeout;
        let selectedPartData = null;

        // Customer management variables
        let customerSearchTimeout;
        let selectedCustomerData = null;

        // Edit mode variables
        let isEditMode = false;
        let currentEditId = null;

        // Initialize currency formatting
        initializeCurrencyFormatting();

        // Manual invoices table variables
        let currentPageManual = 1;

        // Set default date range for manual invoices (current month)
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        document.getElementById('date-from-manual').valueAsDate = firstDay;
        document.getElementById('date-to-manual').valueAsDate = today;

        // Load manual invoices on page load
        loadManualInvoices();

        // Event listeners for manual invoices filters
        document.getElementById('date-from-manual').addEventListener('change', function() {
            currentPageManual = 1;
            loadManualInvoices();
        });

        document.getElementById('date-to-manual').addEventListener('change', function() {
            currentPageManual = 1;
            loadManualInvoices();
        });

        document.getElementById('search-manual-invoices').addEventListener('input', function() {
            currentPageManual = 1;
            loadManualInvoices();
        });

        // Customer search functionality
        const customerInput = document.getElementById('customer');
        const customerSuggestions = document.getElementById('customer-suggestions');

        customerInput.addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(customerSearchTimeout);

            if (query.length < 2) {
                customerSuggestions.style.display = 'none';
                document.getElementById('customer_code').value = '';
                selectedCustomerData = null;
                return;
            }

            customerSearchTimeout = setTimeout(() => {
                searchCustomers(query);
            }, 300);
        });

        // Hide customer suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!customerInput.contains(e.target) && !customerSuggestions.contains(e.target)) {
                customerSuggestions.style.display = 'none';
            }
        });

        function searchCustomers(query) {
            fetch(`/sales/invoices/search-customers?query=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayCustomerSuggestions(data.customers);
                    }
                })
                .catch(error => {
                    console.error('Error searching customers:', error);
                });
        }

        function displayCustomerSuggestions(customers) {
            customerSuggestions.innerHTML = '';

            if (customers.length === 0) {
                customerSuggestions.style.display = 'none';
                return;
            }

            customers.forEach(customer => {
                const item = document.createElement('a');
                item.className = 'dropdown-item';
                item.href = '#';
                item.innerHTML = `
                    <div>
                        <strong>${customer.code}</strong> - ${customer.nama_customer}
                        <br><small class="text-muted">${customer.alamat || 'Alamat tidak tersedia'}</small>
                    </div>
                `;

                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    selectCustomer(customer);
                });

                customerSuggestions.appendChild(item);
            });

            customerSuggestions.style.display = 'block';
        }

        function selectCustomer(customer) {
            selectedCustomerData = customer;
            customerInput.value = customer.nama_customer;
            document.getElementById('customer_code').value = customer.code;
            customerSuggestions.style.display = 'none';
        }

        // Currency formatting functions
        function initializeCurrencyFormatting() {
            const currencyInputs = document.querySelectorAll('.currency-input');
            currencyInputs.forEach(input => {
                addCurrencyFormatting(input);
            });
        }

        function addCurrencyFormatting(input) {
            input.addEventListener('input', function(e) {
                formatCurrency(e.target);
            });

            input.addEventListener('focus', function(e) {
                // Remove formatting on focus for easier editing
                const rawValue = getRawValue(e.target.value);
                e.target.value = rawValue;
                // Update raw value when focusing
                updateRawValue(e.target, parseInt(rawValue) || 0);
            });

            input.addEventListener('blur', function(e) {
                // Apply formatting on blur
                formatCurrency(e.target);
            });

            // Initialize with current value if any
            if (input.value) {
                formatCurrency(input);
            }
        }

        function formatCurrency(input) {
            let value = input.value.replace(/[^\d]/g, ''); // Remove non-digits

            if (value === '') {
                input.value = '';
                updateRawValue(input, 0);
                return;
            }

            // Convert to number and format
            const numValue = parseInt(value);
            const formatted = new Intl.NumberFormat('id-ID').format(numValue);
            input.value = formatted;

            // Update raw value
            updateRawValue(input, numValue);
        }

        function updateRawValue(input, value) {
            const rawInputId = input.id + '-raw';
            const rawInput = document.getElementById(rawInputId);
            if (rawInput) {
                rawInput.value = value;
            }
        }

        function getRawValue(formattedValue) {
            return formattedValue.replace(/[^\d]/g, '');
        }

        function setCurrencyValue(inputId, value) {
            const input = document.getElementById(inputId);
            if (input) {
                // Ensure value is a number
                const numValue = parseFloat(value) || 0;
                const formatted = new Intl.NumberFormat('id-ID').format(numValue);
                input.value = formatted;
                updateRawValue(input, numValue);

                // Trigger input event to ensure any listeners are notified
                input.dispatchEvent(new Event('input', { bubbles: true }));
            }
        }

        // Function to update Indonesian date display
        function updateDateDisplay(inputId) {
            const input = document.getElementById(inputId);
            const displayElement = document.getElementById(inputId + '_display');

            if (input && displayElement && input.value) {
                const date = new Date(input.value);
                if (!isNaN(date.getTime())) {
                    const options = {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric'
                    };
                    const indonesianDate = date.toLocaleDateString('id-ID', options);
                    displayElement.textContent = `Format Indonesia: ${indonesianDate}`;
                } else {
                    displayElement.textContent = '';
                }
            }
        }

        // Add event listener for invoice date change to automatically update due date
        const invoiceDateInput = document.getElementById('tanggal_invoice');
        if (invoiceDateInput) {
            invoiceDateInput.addEventListener('change', function() {
                updateDateDisplay('tanggal_invoice');
                const invoiceDate = new Date(this.value);
                if (!isNaN(invoiceDate.getTime())) {
                    // Calculate due date (60 days from invoice date)
                    const dueDate = new Date(invoiceDate);
                    dueDate.setDate(dueDate.getDate() + 60);
                    document.getElementById('due_date').valueAsDate = dueDate;
                    updateDateDisplay('due_date');
                }
            });
        }

        // Add event listener for due date change
        const dueDateInput = document.getElementById('due_date');
        if (dueDateInput) {
            dueDateInput.addEventListener('change', function() {
                updateDateDisplay('due_date');
            });
        }

        // Parts search functionality
        const partSearchInput = document.getElementById('part-search');
        const partSuggestions = document.getElementById('part-suggestions');

        partSearchInput.addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length < 2) {
                partSuggestions.style.display = 'none';
                return;
            }

            searchTimeout = setTimeout(() => {
                searchParts(query);
            }, 300);
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!partSearchInput.contains(e.target) && !partSuggestions.contains(e.target)) {
                partSuggestions.style.display = 'none';
            }
        });

        function searchParts(query) {
            fetch(`/sales/invoices/search-parts?query=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayPartSuggestions(data.parts);
                    }
                })
                .catch(error => {
                    console.error('Error searching parts:', error);
                });
        }

        function displayPartSuggestions(parts) {
            partSuggestions.innerHTML = '';

            if (parts.length === 0) {
                partSuggestions.style.display = 'none';
                return;
            }

            parts.forEach(part => {
                const item = document.createElement('a');
                item.className = 'dropdown-item';
                item.href = '#';
                item.innerHTML = `
                    <div>
                        <strong>${part.part_code}</strong> - ${part.part_name}
                        <br><small class="text-muted">Harga: Rp ${formatNumber(part.price || 0)} | Tipe: ${part.part_type || 'N/A'}</small>
                    </div>
                `;

                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    selectPart(part);
                });

                partSuggestions.appendChild(item);
            });

            partSuggestions.style.display = 'block';
        }

        function selectPart(part) {
            selectedPartData = part;
            partSearchInput.value = `${part.part_code} - ${part.part_name}`;
            document.getElementById('part-price').value = part.price || 0;
            document.getElementById('part-eum').value = part.eum || 'EA';
            document.getElementById('part-quantity').value = 1;
            partSuggestions.style.display = 'none';
        }

        // Add part to table
        document.getElementById('add-part-btn').addEventListener('click', function() {
            if (!selectedPartData) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Pilih Part',
                    text: 'Silakan pilih part dari hasil pencarian terlebih dahulu'
                });
                return;
            }

            const quantity = parseInt(document.getElementById('part-quantity').value) || 1;
            const price = parseFloat(document.getElementById('part-price').value) || 0;
            const eum = document.getElementById('part-eum').value || 'EA';

            if (quantity <= 0) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Quantity Invalid',
                    text: 'Quantity harus lebih dari 0'
                });
                return;
            }

            if (price < 0) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Harga Invalid',
                    text: 'Harga tidak boleh negatif'
                });
                return;
            }

            // Check if part already exists
            const existingIndex = selectedParts.findIndex(p => p.part_code === selectedPartData.part_code);
            if (existingIndex !== -1) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Part Sudah Ada',
                    text: 'Part ini sudah ditambahkan. Silakan edit quantity jika diperlukan.'
                });
                return;
            }

            const partData = {
                part_code: selectedPartData.part_code,
                part_name: selectedPartData.part_name,
                quantity: quantity,
                price: price,
                eum: eum,
                total: quantity * price
            };

            selectedParts.push(partData);
            updatePartsTable();
            clearPartForm();
        });

        function updatePartsTable() {
            const tbody = document.getElementById('parts-table-body');
            tbody.innerHTML = '';

            let totalAmount = 0;

            selectedParts.forEach((part, index) => {
                totalAmount += part.total;

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${part.part_code}</td>
                    <td>${part.part_name}</td>
                    <td>${part.quantity}</td>
                    <td>Rp ${formatNumber(part.price)}</td>
                    <td>${part.eum}</td>
                    <td>Rp ${formatNumber(part.total)}</td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removePart(${index})">
                            <i class="mdi mdi-delete"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            document.getElementById('parts-total').textContent = `Rp ${formatNumber(totalAmount)}`;
        }

        function clearPartForm() {
            partSearchInput.value = '';
            document.getElementById('part-quantity').value = '';
            document.getElementById('part-price').value = '';
            document.getElementById('part-eum').value = '';
            selectedPartData = null;
        }

        // Global function to remove part
        window.removePart = function(index) {
            selectedParts.splice(index, 1);
            updatePartsTable();
        };

        function formatNumber(num) {
            return new Intl.NumberFormat('id-ID').format(num);
        }

        // Add event listener for auto-generate invoice number button
        const generateInvoiceNumberBtn = document.getElementById('generate-invoice-number');
        if (generateInvoiceNumberBtn) {
            generateInvoiceNumberBtn.addEventListener('click', function() {
                // Show loading indicator
                this.disabled = true;
                this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';

                // Fetch the latest invoice to get the next number
                fetch('/sales/latest-invoice')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.latest_invoice) {
                            // Use the exact format from the last invoice
                            const lastInvoiceNumber = data.latest_invoice.no_invoice;

                            // Extract parts from the invoice number
                            const parts = lastInvoiceNumber.split('/');
                            if (parts.length >= 2) {
                                // Extract the numeric part and increment it
                                const numericPart = parseInt(parts[0]);
                                if (!isNaN(numericPart)) {
                                    const nextNumericPart = numericPart + 1;

                                    // Pad the numeric part to the same length as the original
                                    const originalLength = parts[0].length;
                                    const paddedNumericPart = String(nextNumericPart).padStart(originalLength, '0');

                                    // Get current month and year for the new invoice number
                                    const today = new Date();
                                    const month = String(today.getMonth() + 1).padStart(2, '0');
                                    const year = today.getFullYear();

                                    // Create new invoice number with current month/year
                                    const newInvoiceNumber = `${paddedNumericPart}/INV-PWB/${month}/${year}`;
                                    document.getElementById('no_invoice').value = newInvoiceNumber;
                                }
                            }
                        } else {
                            // Fallback to default format if no invoice exists
                            const today = new Date();
                            const month = String(today.getMonth() + 1).padStart(2, '0');
                            const year = today.getFullYear();
                            document.getElementById('no_invoice').value = `001/INV-PWB/${month}/${year}`;
                        }

                        // Reset button state
                        this.disabled = false;
                        this.innerHTML = 'Auto';
                    })
                    .catch(error => {
                        console.error('Error fetching latest invoice:', error);
                        // Reset button state
                        this.disabled = false;
                        this.innerHTML = 'Auto';
                        // Show error message
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Gagal mengambil nomor invoice terbaru. Silakan coba lagi.'
                        });
                    });
            });
        }

        // Add event listener for form submission
        const invoiceForm = document.getElementById('invoice-form');
        if (invoiceForm) {
            invoiceForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get form data
                const formData = new FormData(this);

                // Use raw values for currency fields - ensure we send numeric values
                const directSubtotalRaw = document.getElementById('direct_subtotal-raw');
                const directSubtotalInput = document.getElementById('direct_subtotal');

                let subtotalValue = 0;
                if (directSubtotalRaw && directSubtotalRaw.value) {
                    subtotalValue = parseInt(directSubtotalRaw.value) || 0;
                } else if (directSubtotalInput && directSubtotalInput.value) {
                    // Extract numeric value from formatted input
                    const numericValue = directSubtotalInput.value.replace(/[^\d]/g, '');
                    subtotalValue = parseInt(numericValue) || 0;
                }

                // Always set the numeric value
                formData.set('direct_subtotal', subtotalValue.toString());

                // Add parts data to form
                if (selectedParts.length > 0) {
                    selectedParts.forEach((part, index) => {
                        formData.append(`parts[${index}][part_code]`, part.part_code);
                        formData.append(`parts[${index}][part_name]`, part.part_name);
                        formData.append(`parts[${index}][quantity]`, part.quantity);
                        formData.append(`parts[${index}][price]`, part.price);
                        formData.append(`parts[${index}][eum]`, part.eum);
                    });
                }

                // Validate required fields
                const invoiceNumber = document.getElementById('no_invoice').value.trim();
                const invoiceDate = document.getElementById('tanggal_invoice').value;
                const subtotalRaw = document.getElementById('direct_subtotal-raw');
                const validationSubtotalValue = subtotalRaw ? parseInt(subtotalRaw.value) : subtotalValue;

                if (!invoiceNumber) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Data Tidak Lengkap',
                        text: 'Nomor Invoice harus diisi',
                        confirmButtonText: 'OK'
                    });
                    return;
                }

                if (!invoiceDate) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Data Tidak Lengkap',
                        text: 'Tanggal Invoice harus diisi',
                        confirmButtonText: 'OK'
                    });
                    return;
                }

                if (!validationSubtotalValue || validationSubtotalValue <= 0) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Data Tidak Lengkap',
                        text: 'Nilai Invoice harus diisi dan lebih dari 0',
                        confirmButtonText: 'OK'
                    });
                    return;
                }

                // Determine if this is create or update
                const url = isEditMode ? `/sales/manual-invoices/${currentEditId}` : '/sales/manual-invoices';
                const actionText = isEditMode ? 'mengupdate' : 'menyimpan';

                // For PUT requests, we need to add the method override
                if (isEditMode) {
                    formData.append('_method', 'PUT');
                }

                // Show loading state
                Swal.fire({
                    title: isEditMode ? 'Mengupdate...' : 'Menyimpan...',
                    text: `Sedang ${actionText} invoice`,
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Add CSRF token
                const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                // Send request to create/update invoice
                fetch(url, {
                    method: 'POST', // Always POST, but with _method override for PUT
                    headers: {
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    Swal.close();

                    if (data.success) {
                        // Show success message
                        Swal.fire({
                            icon: 'success',
                            title: 'Berhasil',
                            text: isEditMode ? 'Invoice berhasil diupdate' : 'Invoice baru berhasil dibuat',
                            timer: 1500,
                            showConfirmButton: false
                        }).then(() => {
                            // Reload manual invoices table
                            loadManualInvoices();
                            // Reset form
                            resetForm();
                        });
                    } else {
                        // Show error message
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: data.message || 'Gagal membuat invoice baru',
                            confirmButtonText: 'OK'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error creating invoice:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Terjadi kesalahan saat membuat invoice baru',
                        confirmButtonText: 'OK'
                    });
                });
            });
        }

        // Manual invoices functions
        function loadManualInvoices() {
            const dateFrom = document.getElementById('date-from-manual').value;
            const dateTo = document.getElementById('date-to-manual').value;
            const search = document.getElementById('search-manual-invoices').value;

            const params = new URLSearchParams({
                page: currentPageManual,
                per_page: 10,
                date_from: dateFrom,
                date_to: dateTo,
                search: search
            });

            fetch(`/sales/manual-invoices?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayManualInvoices(data.invoices);
                        updateManualPagination(data.invoices);
                    } else {
                        console.error('Error loading manual invoices:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error loading manual invoices:', error);
                });
        }

        function displayManualInvoices(invoicesData) {
            const tbody = document.getElementById('manual-invoices-table-body');
            tbody.innerHTML = '';

            if (invoicesData.data.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center">Tidak ada data invoice manual</td></tr>';
                return;
            }

            invoicesData.data.forEach(invoice => {
                const row = document.createElement('tr');

                const partsCount = invoice.manual_invoice_parts ? invoice.manual_invoice_parts.length : 0;
                const partsText = partsCount > 0 ? `${partsCount} parts` : 'Tidak ada parts';

                // Display customer name with code if available
                let customerDisplay = invoice.customer || '-';
                if (invoice.customer_sales && invoice.customer_sales.code) {
                    customerDisplay = `${invoice.customer_sales.code} - ${invoice.customer || invoice.customer_sales.nama_customer}`;
                }

                row.innerHTML = `
                    <td>${formatDate(invoice.tanggal_invoice)}</td>
                    <td>${invoice.no_invoice}</td>
                    <td>${customerDisplay}</td>
                    <td>Rp ${formatNumber(invoice.direct_subtotal)}</td>
                    <td><span class="badge ${getStatusBadgeClass(invoice.payment_status)}">${invoice.payment_status}</span></td>
                    <td>
                        ${partsCount > 0 ?
                            `<button class="btn btn-sm btn-info" onclick="showPartsModal(${invoice.id})">
                                <i class="mdi mdi-format-list-bulleted"></i> ${partsText}
                            </button>` :
                            `<span class="text-muted">${partsText}</span>`
                        }
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-secondary" onclick="editManualInvoice(${invoice.id})" title="Edit">
                                <i class="mdi mdi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="printManualInvoice(${invoice.id})" title="Cetak">
                                <i class="mdi mdi-printer"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // Update showing text
            const showingText = `Menampilkan ${invoicesData.from || 0} - ${invoicesData.to || 0} dari ${invoicesData.total || 0} invoice`;
            document.getElementById('showing-text-manual').textContent = showingText;
        }

        function updateManualPagination(invoicesData) {
            const pagination = document.getElementById('pagination-manual');
            pagination.innerHTML = '';

            if (invoicesData.last_page <= 1) return;

            // Previous button
            if (invoicesData.current_page > 1) {
                const prevLi = document.createElement('li');
                prevLi.className = 'page-item';
                prevLi.innerHTML = `<a class="page-link" href="#" onclick="changeManualPage(${invoicesData.current_page - 1})">Previous</a>`;
                pagination.appendChild(prevLi);
            }

            // Page numbers
            for (let i = 1; i <= invoicesData.last_page; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === invoicesData.current_page ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="changeManualPage(${i})">${i}</a>`;
                pagination.appendChild(li);
            }

            // Next button
            if (invoicesData.current_page < invoicesData.last_page) {
                const nextLi = document.createElement('li');
                nextLi.className = 'page-item';
                nextLi.innerHTML = `<a class="page-link" href="#" onclick="changeManualPage(${invoicesData.current_page + 1})">Next</a>`;
                pagination.appendChild(nextLi);
            }
        }

        window.changeManualPage = function(page) {
            currentPageManual = page;
            loadManualInvoices();
        };

        window.editManualInvoice = function(invoiceId) {
            fetch(`/sales/manual-invoices/${invoiceId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        populateEditForm(data.invoice);
                    }
                })
                .catch(error => {
                    console.error('Error loading invoice for edit:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Gagal memuat data invoice untuk diedit'
                    });
                });
        };

        function populateEditForm(invoice) {
            isEditMode = true;
            currentEditId = invoice.id;

            // Update form title
            document.getElementById('form-title').textContent = 'Edit Invoice';
            document.getElementById('save-invoice-btn').textContent = 'Update Invoice';

            // Populate form fields
            document.getElementById('invoice_id').value = invoice.id;
            document.getElementById('no_invoice').value = invoice.no_invoice;
            document.getElementById('customer').value = invoice.customer || '';
            document.getElementById('customer_code').value = invoice.customer_code || '';
            document.getElementById('po_number').value = invoice.po_number || '';
            document.getElementById('sn').value = invoice.sn || '';
            document.getElementById('trouble').value = invoice.trouble || '';
            document.getElementById('lokasi').value = invoice.lokasi || '';
            document.getElementById('model_unit').value = invoice.model_unit || '';
            document.getElementById('hmkm').value = invoice.hmkm || '';

            // Handle date fields properly - ensure they are in YYYY-MM-DD format
            if (invoice.tanggal_invoice) {
                const invoiceDate = new Date(invoice.tanggal_invoice);
                if (!isNaN(invoiceDate.getTime())) {
                    document.getElementById('tanggal_invoice').value = invoiceDate.toISOString().split('T')[0];
                    updateDateDisplay('tanggal_invoice');
                }
            }

            if (invoice.due_date) {
                const dueDate = new Date(invoice.due_date);
                if (!isNaN(dueDate.getTime())) {
                    document.getElementById('due_date').value = dueDate.toISOString().split('T')[0];
                    updateDateDisplay('due_date');
                }
            }

            document.getElementById('ppn').value = (invoice.ppn * 100) || 11; // Convert decimal to percentage
            document.getElementById('payment_status').value = invoice.payment_status || 'Belum Lunas';
            document.getElementById('notes').value = invoice.notes || '';

            // Populate bank details
            document.getElementById('transfer_to').value = invoice.transfer_to || '';
            document.getElementById('bank_account').value = invoice.bank_account || '';
            document.getElementById('bank_branch').value = invoice.bank_branch || '';

            // Set currency values
            setCurrencyValue('direct_subtotal', invoice.direct_subtotal || 0);

            // Populate parts if any
            selectedParts = [];
            if (invoice.manual_invoice_parts && invoice.manual_invoice_parts.length > 0) {
                invoice.manual_invoice_parts.forEach(part => {
                    selectedParts.push({
                        part_code: part.part_code,
                        part_name: part.part_name,
                        quantity: part.quantity,
                        price: part.price,
                        eum: part.eum,
                        total: part.total
                    });
                });
            }
            updatePartsTable();

            // Scroll to form
            document.getElementById('form-title').scrollIntoView({ behavior: 'smooth' });
        }

        function resetForm() {
            isEditMode = false;
            currentEditId = null;

            // Reset form title
            document.getElementById('form-title').textContent = 'Tambah Invoice';
            document.getElementById('save-invoice-btn').textContent = 'Simpan Invoice';

            // Clear form
            document.getElementById('invoice-form').reset();
            document.getElementById('invoice_id').value = '';
            selectedParts = [];
            updatePartsTable();
            clearPartForm();

            // Clear customer data
            document.getElementById('customer_code').value = '';
            selectedCustomerData = null;

            // Reset date values
            document.getElementById('tanggal_invoice').valueAsDate = today;
            document.getElementById('due_date').valueAsDate = new Date(today.getTime() + 60 * 24 * 60 * 60 * 1000);

            // Update date displays
            updateDateDisplay('tanggal_invoice');
            updateDateDisplay('due_date');

            // Reset currency fields
            setCurrencyValue('direct_subtotal', 0);
        }

        window.printManualInvoice = function(invoiceId) {
            // Open invoice print in new window
            window.open(`/sales/manual-invoices/${invoiceId}/print`, '_blank');
        };

        window.showPartsModal = function(invoiceId) {
            fetch(`/sales/manual-invoices/${invoiceId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayPartsModal(data.invoice);
                    }
                })
                .catch(error => {
                    console.error('Error loading parts details:', error);
                });
        };

        function displayPartsModal(invoice) {
            if (!invoice.manual_invoice_parts || invoice.manual_invoice_parts.length === 0) {
                Swal.fire({
                    icon: 'info',
                    title: 'Tidak Ada Parts',
                    text: 'Invoice ini tidak memiliki parts'
                });
                return;
            }

            let totalParts = 0;
            let partsHtml = `
                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead class="bg-light">
                            <tr>
                                <th>Kode Part</th>
                                <th>Nama Part</th>
                                <th>Qty</th>
                                <th>Harga</th>
                                <th>Satuan</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            invoice.manual_invoice_parts.forEach(part => {
                totalParts += part.total;
                partsHtml += `
                    <tr>
                        <td>${part.part_code}</td>
                        <td>${part.part_name}</td>
                        <td>${part.quantity}</td>
                        <td>Rp ${formatNumber(part.price)}</td>
                        <td>${part.eum}</td>
                        <td>Rp ${formatNumber(part.total)}</td>
                    </tr>
                `;
            });

            partsHtml += `
                        </tbody>
                        <tfoot class="bg-light">
                            <tr>
                                <th colspan="5" class="text-right">Total Parts:</th>
                                <th>Rp ${formatNumber(totalParts)}</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            `;

            Swal.fire({
                title: `Parts untuk Invoice ${invoice.no_invoice}`,
                html: partsHtml,
                width: '80%',
                confirmButtonText: 'Tutup'
            });
        }

        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('id-ID');
        }

        function formatNumber(num) {
            return new Intl.NumberFormat('id-ID').format(num);
        }

        function getStatusBadgeClass(status) {
            switch(status) {
                case 'Lunas': return 'badge-success';
                case 'Belum Lunas': return 'badge-warning';
                case 'Jatuh Tempo': return 'badge-danger';
                default: return 'badge-secondary';
            }
        }
    });
</script>
@endsection
